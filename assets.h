#pragma once

#include <Arduino.h>
#include <gfxfont.h>

#if defined(__AVR__)
    #include <avr/pgmspace.h>
#elif defined(__PIC32MX__)
    #define PROGMEM
#elif defined(__arm__)
    #define PROGMEM
#endif

// 'pacman_food', 2x2px
const uint16_t _PACMAN_FOOD [] PROGMEM = {
    0xfff6, 0xfff6, 0xfff6, 0xfff6
};

// Pacman sprite data (5x5 pixels, 2 animation frames)
const uint16_t PACMAN_CONST [][25] PROGMEM = {
      {
        // 'pacman1, 5x5px
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0x0000, 
        0xFE40, 0xFE40, 0x0000, 0xFE40, 0xFE40, 
        0xFE40, 0xFE40, 0xFE40, 0xFE40, 0xFE40, 
        0xFE40, 0xFE40, 0xFE40, 0xFE40, 0xFE40, 
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0x0000
      }, 
      {
        // 'pacman2, 5x5px (mouth open)
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0xFE40, 
        0xFE40, 0xFE40, 0x0000, 0xFE40, 0x0000, 
        0xFE40, 0xFE40, 0xFE40, 0x0000, 0x0000, 
        0xFE40, 0xFE40, 0xFE40, 0xFE40, 0x0000, 
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0xFE40
      }
    };

// Pacman sprite size constants
const byte PACMAN_IDLE_SIZE[2]  = {5, 5};
const byte PACMAN_JUMP_SIZE[2]  = {5, 5};