#pragma once

#include <WiFi.h>
#include <WebServer.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include "Clockface.h"

class ClockWebServer {
private:
    WebServer* server;
    Clockface* clockface;
    
    // Current settings
    uint16_t pacmanColor = 0xFE40;  // Default yellow
    uint16_t backgroundColor = 0x000E;  // Default sky blue
    uint8_t brightness = 32;
    bool animationEnabled = true;
    
    // Helper functions
    uint16_t hexToRgb565(String hexColor);
    String rgb565ToHex(uint16_t color);
    void handleRoot();
    void handleColorChange();
    void handleBrightnessChange();
    void handleReset();
    void handleToggleAnimation();
    void handleRandom();
    void handleResetGame();
    void handleStatus();
    void handleNotFound();
    
public:
    ClockWebServer(Clockface* cf);
    void begin();
    void handleClient();
    void setPacmanColor(uint16_t color);
    void setBackgroundColor(uint16_t color);
    void setBrightness(uint8_t brightness);
    uint16_t getPacmanColor() { return pacmanColor; }
    uint16_t getBackgroundColor() { return backgroundColor; }
    uint8_t getBrightness() { return brightness; }
    bool isAnimationEnabled() { return animationEnabled; }
};