#pragma once

#include <Arduino.h>
#include "Game.h"
#include "Sprite.h"
#include "Locator.h"
#include "EventBus.h"
#include "assets.h"

const uint8_t PACMAN_PACE = 3;
const uint8_t PACMAN_JUMP_HEIGHT = 14;

class Pacman: public Sprite, public EventTask {
  public:
    enum State {
      MOVING,
      STOPPED,
      TURNING,
      INVENCIBLE
    };

  private:
    uint16_t _PACMAN [2][25] = {
      {
        // 'pacman1, 5x5px
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0x0000,
        0xFE40, 0xFE40, 0x0000, 0xFE40, 0xFE40,
        0xFE40, 0xFE40, 0xFE40, 0xFE40, 0xFE40,
        0xFE40, 0xFE40, 0xFE40, 0xFE40, 0xFE40,
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0x0000
      },
      {
        // 'pacman2, 5x5px
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0xFE40,
        0xFE40, 0xFE40, 0x0000, 0xFE40, 0x0000,
        0xFE40, 0xFE40, 0xFE40, 0x0000, 0x0000,
        0xFE40, 0xFE40, 0xFE40, 0xFE40, 0x0000,
        0x0000, 0xFE40, 0xFE40, 0xFE40, 0xFE40
      }
    };

    byte _iteration = 0;
    Direction direction = Direction::RIGHT;
    int _lastX;
    int _lastY;
    long current_color = 0xFE40;
    const unsigned short* _sprite;
    unsigned long lastMillis = 0;
    unsigned long invencibleTimeout = 0;
    State _state = MOVING; 
    State _lastState = MOVING; 
    bool _pacman_anim = true;
    
    void idle();
    void flip();
    void rotate();
    void changePacmanColor(uint16_t newcolor);

  public:
    Pacman(int x, int y);
    void init();
    void move(Direction dir);
    void turn(Direction dir);
    void setState(State state);
    void jump();
    void update();
    const char* name();
    void execute(EventType event, Sprite* caller);
    Direction _direction = Direction::RIGHT;
    const int SPRITE_SIZE = 5;

  public:
    void setColor(uint16_t newColor);
    uint16_t getColor() { return current_color; }

};