#ifndef HOUR_FONT_H
#define HOUR_FONT_H

#include <Arduino.h>
#include <gfxfont.h>
const uint8_t hourFontBitmaps[] PROGMEM = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFC, 0x78, 0xFF, 0xFF, 
  0xFF, 0xFF, 0xF8, 0x38, 0x73, 0xE7, 0xC3, 0x87, 0x0E, 0x1C, 0xFF, 0xFF, 
  0xF8, 0xFF, 0xFC, 0x18, 0x3F, 0xFF, 0xFC, 0x78, 0xFF, 0xFF, 0xF8, 0x00, 
  0xFF, 0xFC, 0x78, 0xFF, 0xFF, 0xC7, 0x8F, 0xFF, 0xFF, 0xF8, 0x00, 0xC7, 
  0x8F, 0xFF, 0xFF, 0xFF, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0xFF, 0xFF, 0xC7, 
  0x8F, 0xFF, 0xC1, 0x83, 0xFF, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0x06, 0x0F, 
  0xFF, 0xFF, 0xE7, 0xCF, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0x60, 
  0xC1, 0x83, 0x06, 0x0C, 0x18, 0x01, 0xFF, 0xFE, 0x3C, 0x7F, 0xFF, 0xE3, 
  0xC7, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0x9F, 0x3F, 0xFF, 0xFF, 0x83, 0x07, 
  0xFF, 0xF8, 0x00, 0xF0, 0x3C, 0x00
};

const GFXglyph hourFontGlyphs[] PROGMEM = {
  {     0,   3,  11,   3,    0,   -6 },   // 0x20 ' '
  {     0,   0,   0,   0,    0,    0 },   // 0x21 '!'
  {     0,   0,   0,   0,    0,    0 },   // 0x22 '"'
  {     0,   0,   0,   0,    0,    0 },   // 0x23 '#'
  {     0,   0,   0,   0,    0,    0 },   // 0x24 '$'
  {     0,   0,   0,   0,    0,    0 },   // 0x25 '%'
  {     0,   0,   0,   0,    0,    0 },   // 0x26 '&'
  {     0,   0,   0,   0,    0,    0 },   // 0x27 '''
  {     0,   0,   0,   0,    0,    0 },   // 0x28 '('
  {     0,   0,   0,   0,    0,    0 },   // 0x29 ')'
  {     0,   0,   0,   0,    0,    0 },   // 0x2A '*'
  {     0,   0,   0,   0,    0,    0 },   // 0x2B '+'
  {     0,   0,   0,   0,    0,    0 },   // 0x2C ','
  {     0,   0,   0,   0,    0,    0 },   // 0x2D '-'
  {     0,   0,   0,   0,    0,    0 },   // 0x2E '.'
  {     0,   0,   0,   0,    0,    0 },   // 0x2F '/'
  {     5,   7,  11,   8,    0,   -6 },   // 0x30 '0'
  {    15,   7,  11,   8,    0,   -6 },   // 0x31 '1'
  {    25,   7,  11,   8,    0,   -6 },   // 0x32 '2'
  {    36,   7,  11,   8,    0,   -6 },   // 0x33 '3'
  {    47,   7,  11,   8,    0,   -6 },   // 0x34 '4'
  {    57,   7,  11,   8,    0,   -6 },   // 0x35 '5'
  {    68,   7,  11,   8,    0,   -6 },   // 0x36 '6'
  {    79,   7,  11,   8,    0,   -6 },   // 0x37 '7'
  {    89,   7,  12,   8,    0,   -7 },   // 0x38 '8'
  {   100,   7,  12,   8,    0,   -6 },   // 0x39 '9'
  {   111,   2,   7,   3,    0,   -4 },   // 0x3A ':'
  {     0,   0,   0,   0,    0,    0 },   // 0x3B ';'
  {     0,   0,   0,   0,    0,    0 },   // 0x3C '<'
  {     0,   0,   0,   0,    0,    0 },   // 0x3D '='
  {     0,   0,   0,   0,    0,    0 },   // 0x3E '>'
  {     0,   0,   0,   0,    0,    0 },   // 0x3F '?'
  {     0,   0,   0,   0,    0,    0 },   // 0x40 '@'
  {     0,   0,   0,   0,    0,    0 },   // 0x41 'A'
  {     0,   0,   0,   0,    0,    0 },   // 0x42 'B'
  {     0,   0,   0,   0,    0,    0 },   // 0x43 'C'
  {     0,   0,   0,   0,    0,    0 },   // 0x44 'D'
  {     0,   0,   0,   0,    0,    0 },   // 0x45 'E'
  {     0,   0,   0,   0,    0,    0 },   // 0x46 'F'
  {     0,   0,   0,   0,    0,    0 },   // 0x47 'G'
  {     0,   0,   0,   0,    0,    0 },   // 0x48 'H'
  {     0,   0,   0,   0,    0,    0 },   // 0x49 'I'
  {     0,   0,   0,   0,    0,    0 },   // 0x4A 'J'
  {     0,   0,   0,   0,    0,    0 },   // 0x4B 'K'
  {     0,   0,   0,   0,    0,    0 },   // 0x4C 'L'
  {     0,   0,   0,   0,    0,    0 },   // 0x4D 'M'
  {     0,   0,   0,   0,    0,    0 },   // 0x4E 'N'
  {     0,   0,   0,   0,    0,    0 },   // 0x4F 'O'
  {     0,   0,   0,   0,    0,    0 },   // 0x50 'P'
  {     0,   0,   0,   0,    0,    0 },   // 0x51 'Q'
  {     0,   0,   0,   0,    0,    0 },   // 0x52 'R'
  {     0,   0,   0,   0,    0,    0 },   // 0x53 'S'
  {     0,   0,   0,   0,    0,    0 },   // 0x54 'T'
  {     0,   0,   0,   0,    0,    0 },   // 0x55 'U'
  {     0,   0,   0,   0,    0,    0 },   // 0x56 'V'
  {     0,   0,   0,   0,    0,    0 },   // 0x57 'W'
  {     0,   0,   0,   0,    0,    0 },   // 0x58 'X'
  {     0,   0,   0,   0,    0,    0 },   // 0x59 'Y'
  {     0,   0,   0,   0,    0,    0 },   // 0x5A 'Z'
  {     0,   0,   0,   0,    0,    0 },   // 0x5B '['
  {     0,   0,   0,   0,    0,    0 },   // 0x5C '\'
  {     0,   0,   0,   0,    0,    0 },   // 0x5D ']'
  {     0,   0,   0,   0,    0,    0 },   // 0x5E '^'
  {     0,   0,   0,   0,    0,    0 },   // 0x5F '_'
  {     0,   0,   0,   0,    0,    0 },   // 0x60 '`'
  {     0,   0,   0,   0,    0,    0 },   // 0x61 'a'
  {     0,   0,   0,   0,    0,    0 },   // 0x62 'b'
  {     0,   0,   0,   0,    0,    0 },   // 0x63 'c'
  {     0,   0,   0,   0,    0,    0 },   // 0x64 'd'
  {     0,   0,   0,   0,    0,    0 },   // 0x65 'e'
  {     0,   0,   0,   0,    0,    0 },   // 0x66 'f'
  {     0,   0,   0,   0,    0,    0 },   // 0x67 'g'
  {     0,   0,   0,   0,    0,    0 },   // 0x68 'h'
  {     0,   0,   0,   0,    0,    0 },   // 0x69 'i'
  {     0,   0,   0,   0,    0,    0 },   // 0x6A 'j'
  {     0,   0,   0,   0,    0,    0 },   // 0x6B 'k'
  {     0,   0,   0,   0,    0,    0 },   // 0x6C 'l'
  {     0,   0,   0,   0,    0,    0 },   // 0x6D 'm'
  {     0,   0,   0,   0,    0,    0 },   // 0x6E 'n'
  {     0,   0,   0,   0,    0,    0 },   // 0x6F 'o'
  {     0,   0,   0,   0,    0,    0 },   // 0x70 'p'
  {     0,   0,   0,   0,    0,    0 },   // 0x71 'q'
  {     0,   0,   0,   0,    0,    0 },   // 0x72 'r'
  {     0,   0,   0,   0,    0,    0 },   // 0x73 's'
  {     0,   0,   0,   0,    0,    0 },   // 0x74 't'
  {     0,   0,   0,   0,    0,    0 },   // 0x75 'u'
  {     0,   0,   0,   0,    0,    0 },   // 0x76 'v'
  {     0,   0,   0,   0,    0,    0 },   // 0x77 'w'
  {     0,   0,   0,   0,    0,    0 },   // 0x78 'x'
  {     0,   0,   0,   0,    0,    0 },   // 0x79 'y'
  {     0,   0,   0,   0,    0,    0 },   // 0x7A 'z'
  {     0,   0,   0,   0,    0,    0 },   // 0x7B '{'
  {     0,   0,   0,   0,    0,    0 },   // 0x7C '|'
  {     0,   0,   0,   0,    0,    0 },   // 0x7D '}'
  {     0,   0,   0,   0,    0,    0 }    // 0x7E '~'
};

const GFXfont hourFont PROGMEM = {
  (uint8_t  *)hourFontBitmaps,
  (GFXglyph *)hourFontGlyphs, 0x20, 0x7E,   11 };

// Approx. 1268 bytes

#endif
