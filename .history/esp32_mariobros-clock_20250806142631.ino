//参考文章：https://blog.csdn.net/u011625956/article/details/136828317
//https://www.instructables.com/Mario-Bros-Clock/
//https://github.com/jnthas/mariobros-clock

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "Clockface.h"
#include "WiFiConnect.h"
#include "CWDateTime.h"

MatrixPanel_I2S_DMA *dma_display = nullptr;
WiFiConnect wifi;
CWDateTime cwDateTime;
Clockface *clockface;

uint16_t myBLACK = dma_display->color565(0, 0, 0);
uint16_t myWHITE = dma_display->color565(255, 255, 255);
uint16_t myBLUE = dma_display->color565(0, 0, 255);

byte displayBright = 32;

void displaySetup() {
  HUB75_I2S_CFG mxconfig(
    128,   // module width
    64,   // module height
    1    // Chain length
  );

  mxconfig.gpio.e = 32;
  mxconfig.clkphase = false;

/* 
#if HUB75_BLUE_GREEN_SWAP
  // Swap Blue and Green pins because the panel is RBG instead of RGB.
  mxconfig.gpio.b1 = 26;
  mxconfig.gpio.b2 = 12;
  mxconfig.gpio.g1 = 27;
  mxconfig.gpio.g2 = 13;
#endif
*/

  // Display Setup
  dma_display = new MatrixPanel_I2S_DMA(mxconfig);
  dma_display->begin();
  dma_display->setBrightness8(displayBright);
  dma_display->clearScreen();
  dma_display->fillScreen(myBLACK);
}

void setup() {

  Serial.begin(115200);

  displaySetup();

  clockface = new Clockface(dma_display);

  dma_display->setTextSize(1);
  dma_display->setTextColor(myWHITE);
  dma_display->setCursor(5, 0);
  dma_display->println("CLOCKWISE");
  dma_display->setTextColor(myBLUE);
  dma_display->setCursor(0, 32);
  dma_display->print("connecting...");

  wifi.connect();
  cwDateTime.begin();

  clockface->setup(&cwDateTime);
}

void loop() {
  clockface->update();

}
