#include "MyWebServer.h"
#include "Locator.h"

ClockWebServer::ClockWebServer(Clockface* cf) {
    server = new WebServer(80);
    clockface = cf;
}

void ClockWebServer::begin() {
    // Initialize SPIFFS
    if (!SPIFFS.begin(true)) {
        Serial.println("An Error has occurred while mounting SPIFFS");
        return;
    }

    // Set up route handlers
    server->on("/", HTTP_GET, [this]() { this->handleRoot(); });
    server->on("/api/color", HTTP_POST, [this]() { this->handleColorChange(); });
    server->on("/api/brightness", HTTP_POST, [this]() { this->handleBrightnessChange(); });
    server->on("/api/reset", HTTP_POST, [this]() { this->handleReset(); });
    server->on("/api/toggle-animation", HTTP_POST, [this]() { this->handleToggleAnimation(); });
    server->on("/api/random", HTTP_POST, [this]() { this->handleRandom(); });
    server->on("/api/reset-game", HTTP_POST, [this]() { this->handleResetGame(); });
    server->on("/api/status", HTTP_GET, [this]() { this->handleStatus(); });
    server->onNotFound([this]() { this->handleNotFound(); });

    server->begin();
    Serial.println("Web server started on port 80");
}

void ClockWebServer::handleClient() {
    server->handleClient();
}

void ClockWebServer::handleRoot() {
    File file = SPIFFS.open("/index.html", "r");
    if (!file) {
        server->send(404, "text/plain", "File not found");
        return;
    }
    
    server->streamFile(file, "text/html");
    file.close();
}

void ClockWebServer::handleColorChange() {
    if (server->hasArg("plain")) {
        DynamicJsonDocument doc(1024);
        deserializeJson(doc, server->arg("plain"));
        
        String target = doc["target"];
        String colorHex = doc["color"];
        
        uint16_t color = hexToRgb565(colorHex);
        
        if (target == "pacman") {
            setPacmanColor(color);
        } else if (target == "background") {
            setBackgroundColor(color);
        }
        
        DynamicJsonDocument response(256);
        response["success"] = true;
        response["target"] = target;
        response["color"] = colorHex;
        
        String responseStr;
        serializeJson(response, responseStr);
        server->send(200, "application/json", responseStr);
    } else {
        server->send(400, "application/json", "{\"error\":\"Invalid request\"}");
    }
}

void ClockWebServer::handleBrightnessChange() {
    if (server->hasArg("plain")) {
        DynamicJsonDocument doc(1024);
        deserializeJson(doc, server->arg("plain"));
        
        uint8_t newBrightness = doc["brightness"];
        setBrightness(newBrightness);
        
        DynamicJsonDocument response(256);
        response["success"] = true;
        response["brightness"] = newBrightness;
        
        String responseStr;
        serializeJson(response, responseStr);
        server->send(200, "application/json", responseStr);
    } else {
        server->send(400, "application/json", "{\"error\":\"Invalid request\"}");
    }
}

void ClockWebServer::handleReset() {
    // Reset to default colors
    setPacmanColor(0xFE40);  // Default yellow
    setBackgroundColor(0x000E);  // Default sky blue
    setBrightness(32);  // Default brightness
    
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Colors reset to default";
    
    String responseStr;
    serializeJson(response, responseStr);
    server->send(200, "application/json", responseStr);
}

void ClockWebServer::handleToggleAnimation() {
    animationEnabled = !animationEnabled;
    
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["animationEnabled"] = animationEnabled;
    
    String responseStr;
    serializeJson(response, responseStr);
    server->send(200, "application/json", responseStr);
}

void ClockWebServer::handleRandom() {
    // Generate random colors
    uint16_t randomPacman = random(0x10000);
    uint16_t randomBackground = random(0x10000);
    
    setPacmanColor(randomPacman);
    setBackgroundColor(randomBackground);
    
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["pacmanColor"] = rgb565ToHex(randomPacman);
    response["backgroundColor"] = rgb565ToHex(randomBackground);
    
    String responseStr;
    serializeJson(response, responseStr);
    server->send(200, "application/json", responseStr);
}

void ClockWebServer::handleResetGame() {
    // Reset the Pacman game - restore all food
    if (clockface) {
        clockface->resetMap();
    }
    
    DynamicJsonDocument response(256);
    response["success"] = true;
    response["message"] = "Game reset - all food restored";
    
    String responseStr;
    serializeJson(response, responseStr);
    server->send(200, "application/json", responseStr);
}

void ClockWebServer::handleStatus() {
    DynamicJsonDocument response(512);
    response["pacmanColor"] = rgb565ToHex(pacmanColor);
    response["backgroundColor"] = rgb565ToHex(backgroundColor);
    response["brightness"] = brightness;
    response["animationEnabled"] = animationEnabled;
    response["uptime"] = millis();
    
    String responseStr;
    serializeJson(response, responseStr);
    server->send(200, "application/json", responseStr);
}

void ClockWebServer::handleNotFound() {
    String path = server->uri();
    
    // Try to serve file from SPIFFS
    if (SPIFFS.exists(path)) {
        File file = SPIFFS.open(path, "r");
        String contentType = "text/plain";
        
        if (path.endsWith(".html")) contentType = "text/html";
        else if (path.endsWith(".css")) contentType = "text/css";
        else if (path.endsWith(".js")) contentType = "application/javascript";
        else if (path.endsWith(".png")) contentType = "image/png";
        else if (path.endsWith(".jpg")) contentType = "image/jpeg";
        else if (path.endsWith(".ico")) contentType = "image/x-icon";
        
        server->streamFile(file, contentType);
        file.close();
    } else {
        server->send(404, "text/plain", "File not found: " + path);
    }
}

uint16_t ClockWebServer::hexToRgb565(String hexColor) {
    // Remove '#' if present
    if (hexColor.startsWith("#")) {
        hexColor = hexColor.substring(1);
    }
    
    // Convert hex to RGB
    long hex = strtol(hexColor.c_str(), nullptr, 16);
    uint8_t r = (hex >> 16) & 0xFF;
    uint8_t g = (hex >> 8) & 0xFF;
    uint8_t b = hex & 0xFF;
    
    // Convert to RGB565
    return ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
}

String ClockWebServer::rgb565ToHex(uint16_t color) {
    uint8_t r = (color >> 8) & 0xF8;
    uint8_t g = (color >> 3) & 0xFC;
    uint8_t b = (color << 3) & 0xF8;
    
    char hex[8];
    sprintf(hex, "#%02X%02X%02X", r, g, b);
    return String(hex);
}

void ClockWebServer::setPacmanColor(uint16_t color) {
    pacmanColor = color;
    if (clockface) {
        clockface->setPacmanColor(color);
    }
}

void ClockWebServer::setBackgroundColor(uint16_t color) {
    backgroundColor = color;
    if (clockface) {
        clockface->setBackgroundColor(color);
    }
}

void ClockWebServer::setBrightness(uint8_t newBrightness) {
    brightness = newBrightness;
    if (Locator::getDisplay()) {
        // For HUB75 displays, we need to access the actual display object
        // This would need to be passed through somehow, for now just store the value
    }
}