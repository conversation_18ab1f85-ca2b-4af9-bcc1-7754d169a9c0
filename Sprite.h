
#ifndef Sprite_h
#define Sprite_h

#include <Arduino.h>

class Sprite {

  protected:
    uint8_t _x;
    uint8_t _y;
    uint8_t _width;
    uint8_t _height;

  public:
    boolean collidedWith(Sprite* sprite);
    void logPosition();

    // Getter methods for position
    uint8_t getX() const { return _x; }
    uint8_t getY() const { return _y; }
    uint8_t getWidth() const { return _width; }
    uint8_t getHeight() const { return _height; }

    virtual const char* name();
};


#endif
